import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import { invoke } from '@tauri-apps/api/core';
import { Product, ProductSearchResult, PantryItem } from '@app-types';

interface ProductSearchModalProps {
  open: boolean;
  onClose: () => void;
  onAddProduct: (item: PantryItem) => void;
}

const categories = [
  'Produce', 'Dairy', 'Meat', 'Grains', 'Baking', 'Spices', 'Canned Goods', 'Frozen', 'Other'
];

const units = [
  'g', 'kg', 'ml', 'l', 'cups', 'tbsp', 'tsp', 'oz', 'lb', 'piece(s)'
];

const ProductSearchModal: React.FC<ProductSearchModalProps> = ({
  open,
  onClose,
  onAddProduct,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [amount, setAmount] = useState(1);
  const [unit, setUnit] = useState('piece(s)');
  const [category, setCategory] = useState('Other');
  const [expiryDate, setExpiryDate] = useState('');

  // Debounced search
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    const timeoutId = setTimeout(async () => {
      await performSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const performSearch = async (query: string) => {
    if (!query.trim()) return;

    setLoading(true);
    setError(null);

    try {
      const result = await invoke<ProductSearchResult>('db_search_products', {
        query: query.trim(),
        limit: 10,
      });

      setSearchResults(result.products);
    } catch (err) {
      console.error('Error searching products:', err);
      setError('Failed to search products. Please try again.');
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  const handleProductSelect = (product: Product) => {
    setSelectedProduct(product);
  };

  const handleAddToPantry = () => {
    if (!selectedProduct) return;

    const pantryItem: PantryItem = {
      id: crypto.randomUUID(),
      name: selectedProduct.product_name,
      amount,
      unit,
      category,
      expiryDate: expiryDate || undefined,
      dateAdded: new Date().toISOString(),
      dateModified: new Date().toISOString(),
      productCode: selectedProduct.code,
      productName: selectedProduct.product_name,
      brands: selectedProduct.brands,
    };

    onAddProduct(pantryItem);
    handleClose();
  };

  const handleClose = () => {
    setSearchQuery('');
    setSearchResults([]);
    setSelectedProduct(null);
    setAmount(1);
    setUnit('piece(s)');
    setCategory('Other');
    setExpiryDate('');
    setError(null);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>Add Product to Pantry</DialogTitle>
      <DialogContent>
        <Box sx={{ mt: 1 }}>
          {/* Search Field */}
          <TextField
            autoFocus
            label="Search by UPC code, product name, or brand"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            fullWidth
            margin="normal"
            placeholder="e.g., 123456789012, Coca Cola, Heinz"
            data-testid="product-search-input"
          />

          {/* Loading Indicator */}
          {loading && (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
              <CircularProgress size={24} />
            </Box>
          )}

          {/* Error Message */}
          {error && (
            <Alert severity="error" sx={{ my: 2 }}>
              {error}
            </Alert>
          )}

          {/* Search Results */}
          {searchResults.length > 0 && !selectedProduct && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Search Results ({searchResults.length})
              </Typography>
              <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                {searchResults.map((product) => (
                  <ListItem key={product.code} disablePadding>
                    <ListItemButton
                      onClick={() => handleProductSelect(product)}
                      data-testid={`product-result-${product.code}`}
                    >
                      <ListItemText
                        primary={product.product_name}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Brand: {product.brands}
                            </Typography>
                            <Chip
                              label={`UPC: ${product.code}`}
                              size="small"
                              variant="outlined"
                              sx={{ mt: 0.5 }}
                            />
                          </Box>
                        }
                      />
                    </ListItemButton>
                  </ListItem>
                ))}
              </List>
            </Box>
          )}

          {/* Selected Product Details */}
          {selectedProduct && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="h6" gutterBottom>
                Selected Product
              </Typography>
              <Box sx={{ p: 2, bgcolor: 'background.default', borderRadius: 1, mb: 2 }}>
                <Typography variant="subtitle1" fontWeight="bold">
                  {selectedProduct.product_name}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Brand: {selectedProduct.brands}
                </Typography>
                <Chip
                  label={`UPC: ${selectedProduct.code}`}
                  size="small"
                  variant="outlined"
                  sx={{ mt: 1 }}
                />
              </Box>

              {/* Pantry Item Details */}
              <Typography variant="h6" gutterBottom>
                Pantry Details
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <TextField
                    label="Amount"
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(Number(e.target.value))}
                    sx={{ flex: 1 }}
                    inputProps={{ min: 0, step: 0.1 }}
                    data-testid="product-amount-input"
                  />
                  <FormControl sx={{ flex: 1 }}>
                    <InputLabel>Unit</InputLabel>
                    <Select
                      value={unit}
                      onChange={(e) => setUnit(e.target.value)}
                      label="Unit"
                      data-testid="product-unit-select"
                    >
                      {units.map((u) => (
                        <MenuItem key={u} value={u}>
                          {u}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
                <FormControl fullWidth>
                  <InputLabel>Category</InputLabel>
                  <Select
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    label="Category"
                    data-testid="product-category-select"
                  >
                    {categories.map((c) => (
                      <MenuItem key={c} value={c}>
                        {c}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <TextField
                  label="Expiry Date"
                  type="date"
                  value={expiryDate}
                  onChange={(e) => setExpiryDate(e.target.value)}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  data-testid="product-expiry-input"
                />
              </Box>

              <Button
                variant="outlined"
                onClick={() => setSelectedProduct(null)}
                sx={{ mt: 2 }}
                data-testid="product-back-button"
              >
                Back to Search Results
              </Button>
            </Box>
          )}

          {/* No Results Message */}
          {searchQuery.trim() && !loading && searchResults.length === 0 && !error && (
            <Box sx={{ mt: 2, textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                No products found for "{searchQuery}". Try a different search term.
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} data-testid="product-cancel-button">
          Cancel
        </Button>
        <Button
          onClick={handleAddToPantry}
          variant="contained"
          disabled={!selectedProduct}
          data-testid="product-add-button"
        >
          Add to Pantry
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProductSearchModal;
